#!/bin/bash

# Malaria Surveillance Assessment Toolkit - Full Stack Startup Script
# This script starts both the frontend (React/Vite) and backend (.NET) servers

echo "🚀 Starting Malaria Surveillance Assessment Toolkit..."
echo "📋 This will start both frontend and backend servers"
echo ""

# Function to cleanup processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down servers..."
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        echo "✅ Frontend server stopped"
    fi
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        echo "✅ Backend server stopped"
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start Frontend (React/Vite) in background
echo "🎨 Starting Frontend (React/Vite) on port 3000..."
cd "WHO.MALARIA.Web/malaria-client"
npm start &
FRONTEND_PID=$!
cd ../..

# Wait a moment for frontend to start
sleep 3

# Start Backend (.NET) in background
echo "⚙️  Starting Backend (.NET) on port 5001..."
dotnet run --project WHO.MALARIA.Web &
BACKEND_PID=$!

# Wait for both servers to be ready
echo ""
echo "⏳ Waiting for servers to start..."
sleep 5

# Check if servers are running
echo ""
echo "🔍 Checking server status..."

# Check frontend
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Frontend server is running at http://localhost:3000"
else
    echo "❌ Frontend server failed to start"
fi

# Check backend
if curl -k -s https://localhost:5001 > /dev/null; then
    echo "✅ Backend server is running at https://localhost:5001"
else
    echo "❌ Backend server failed to start"
fi

echo ""
echo "🌐 Application is ready!"
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend:  https://localhost:5001"
echo "🎯 Main App: https://localhost:5001 (with frontend proxy)"
echo ""
echo "Press Ctrl+C to stop both servers"

# Wait for user to stop the servers
wait
