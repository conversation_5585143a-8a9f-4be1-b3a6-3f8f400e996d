{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppSettings": {"ShowErrorDetails": true, "SupportUrl": "", "SupportEmail": "", "StatsPollingInterval": 3000, "QueuePollingInterval ": 600000000, "UpdatePasswordLinkExpiry": "48", "CookieExpireTimeSpan": 15, "TokenRefreshThreshold": 300, "RestrictBusinessEntityModification": true, "AzureAD": {"ClientId": "", "ClientSecret": "", "TenantId": ""}, "Email": {"EmailSender": "<EMAIL>", "SmtpHost": "smtp.live.com", "SmtpPort": "587", "UserName": "<EMAIL>", "Password": "malaria@12345"}, "SendGrid": {"From": "<EMAIL>"}, "Cloudmersive": {"ApiKey": "************************************"}, "AnalyticsFields": {"TrackingId": "xxxx", "PrivateKey": "xxxx", "ClientEmail": "xxxx", "WebsiteCode": "xxxx"}}}